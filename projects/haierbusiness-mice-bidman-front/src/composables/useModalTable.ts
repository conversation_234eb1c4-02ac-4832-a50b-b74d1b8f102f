import { ref, computed } from 'vue'
import { usePagination } from 'vue-request'
import { useTableSelection } from './useTableSelection'
import type { Key } from 'ant-design-vue/lib/vc-table/interface'

interface PageResult<T> {
  records: T[]
  total: number
  pageNum: number
  pageSize: number
}

/**
 * 弹窗表格 Hook - 集成选择功能和分页
 * @param apiFunction API 函数
 * @param options 配置选项
 */
export function useModalTable<T extends Record<string, any>, P extends Record<string, any>>(
  apiFunction: (params: P & { pageNum: number; pageSize: number }) => Promise<PageResult<T>>,
  options?: {
    autoLoad?: boolean
    defaultPageSize?: number
    selectionType?: 'checkbox' | 'radio'
    preserveSelection?: boolean
  }
) {
  const visible = ref(false)
  const searchParams = ref<Partial<P>>({})
  
  // 使用表格选择 Hook
  const {
    selectedRowKeys,
    selectedRows,
    rowSelection,
    clearSelection,
    setSelection,
    hasSelection,
    selectionCount
  } = useTableSelection<T>({
    type: options?.selectionType || 'checkbox',
    preserveSelectedRowKeys: options?.preserveSelection
  })

  // 使用分页 Hook
  const { 
    data, 
    loading, 
    run,
    current,
    pageSize 
  } = usePagination(apiFunction, {
    manual: !options?.autoLoad,
    defaultParams: [{
      pageNum: 1,
      pageSize: options?.defaultPageSize || 10
    } as P & { pageNum: number; pageSize: number }]
  })

  // 计算分页配置
  const pagination = computed(() => ({
    showSizeChanger: true,
    showQuickJumper: true,
    total: data.value?.total || 0,
    current: data.value?.pageNum || 1,
    pageSize: data.value?.pageSize || 10,
    style: { justifyContent: 'center' }
  }))

  // 处理表格变化（分页、排序等）
  const handleTableChange = (pag: any, filters?: any, sorter?: any) => {
    run({
      ...searchParams.value,
      pageNum: pag.current || 1,
      pageSize: pag.pageSize || 10
    } as P & { pageNum: number; pageSize: number })
  }

  // 搜索
  const search = (params?: Partial<P>) => {
    if (params) {
      searchParams.value = { ...searchParams.value, ...params }
    }
    run({
      ...searchParams.value,
      pageNum: 1,
      pageSize: data.value?.pageSize || 10
    } as P & { pageNum: number; pageSize: number })
  }

  // 打开弹窗
  const open = (params?: Partial<P>) => {
    visible.value = true
    clearSelection()
    if (params) {
      searchParams.value = params
      search(params)
    } else if (!options?.autoLoad) {
      search()
    }
  }

  // 关闭弹窗
  const close = () => {
    visible.value = false
    clearSelection()
    searchParams.value = {}
  }

  // 重置
  const reset = () => {
    searchParams.value = {}
    clearSelection()
    search()
  }

  return {
    // 弹窗状态
    visible,
    
    // 表格数据
    data,
    loading,
    pagination,
    
    // 选择功能
    selectedRowKeys,
    selectedRows,
    rowSelection,
    hasSelection,
    selectionCount,
    
    // 操作方法
    open,
    close,
    search,
    reset,
    handleTableChange,
    clearSelection,
    setSelection
  }
}