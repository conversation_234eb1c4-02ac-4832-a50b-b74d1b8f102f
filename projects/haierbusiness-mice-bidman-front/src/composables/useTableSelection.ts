import { ref, computed, readonly } from 'vue'
import type { Key } from 'ant-design-vue/lib/vc-table/interface'
import type { TableProps } from 'ant-design-vue'

/**
 * 表格选择 Hook - 解决所有常见的响应式问题
 * @param options 配置选项
 */
export function useTableSelection<T extends Record<string, any>>(options?: {
  type?: 'checkbox' | 'radio'
  preserveSelectedRowKeys?: boolean
  getCheckboxProps?: (record: T) => Record<string, any>
}) {
  const selectedRowKeys = ref<Key[]>([])
  const selectedRows = ref<T[]>([])

  // 稳定的 rowSelection 对象，避免重新创建
  const rowSelection: TableProps['rowSelection'] = {
    type: options?.type || 'checkbox',
    selectedRowKeys: selectedRowKeys, // 直接传 ref，不使用 .value
    preserveSelectedRowKeys: options?.preserveSelectedRowKeys,
    getCheckboxProps: options?.getCheckboxProps,
    onChange: (keys: Key[], rows: T[]) => {
      selectedRowKeys.value = keys
      selectedRows.value = rows
    }
  }

  // 清空选择
  const clearSelection = () => {
    selectedRowKeys.value = []
    selectedRows.value = []
  }

  // 设置选择
  const setSelection = (keys: Key[], rows: T[]) => {
    selectedRowKeys.value = keys
    selectedRows.value = rows
  }

  // 判断是否有选择
  const hasSelection = computed(() => selectedRowKeys.value.length > 0)

  // 选择数量
  const selectionCount = computed(() => selectedRowKeys.value.length)

  return {
    selectedRowKeys: readonly(selectedRowKeys),
    selectedRows: readonly(selectedRows),
    rowSelection,
    clearSelection,
    setSelection,
    hasSelection,
    selectionCount
  }
}