<!-- 公共操作栏组件 -->
<template>
  <a-popover
    :trigger="trigger"
    :placement="placement"
    :overlayClassName="overlayClassName"
    :getPopupContainer="getPopupContainer"
  >
    <!-- 触发器插槽 -->
    <template #content>
      <div :class="['action-popover-content', `layout-${layout}`]" :style="{ maxWidth: maxWidth }">
        <template v-for="action in visibleActions" :key="action.key">
          <!-- 分割线 -->
          <a-divider v-if="action.type === 'divider'" :style="{ margin: layout === 'horizontal' ? '0 8px' : '8px 0' }" />
          
          <!-- 操作按钮 -->
          <a-button
            v-else
            :type="action.buttonType || 'text'"
            :danger="action.danger"
            :disabled="getDisabled(action)"
            :size="size"
            :class="['action-item', action.className]"
            @click="handleActionClick(action)"
          >
            <component v-if="action.icon" :is="action.icon" class="action-icon" />
            {{ action.label }}
          </a-button>
        </template>
      </div>
    </template>
    
    <!-- 默认触发器 -->
    <slot>
      <a-button type="link" size="small">
        <MoreOutlined />
        操作
      </a-button>
    </slot>
  </a-popover>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Modal, message } from 'ant-design-vue';
import { MoreOutlined } from '@ant-design/icons-vue';
import type { Component } from 'vue';

// 操作项接口定义
export interface ActionItem {
  key: string;
  label: string;
  icon?: Component | string;
  buttonType?: 'text' | 'link' | 'primary' | 'default';
  danger?: boolean;
  disabled?: boolean | ((record: any) => boolean);
  visible?: boolean | ((record: any) => boolean);
  className?: string;
  confirm?: {
    title: string;
    content?: string;
    okText?: string;
    cancelText?: string;
  };
  onClick: (record: any, key: string) => void | Promise<void>;
  type?: 'action' | 'divider'; // 支持分割线
}

// Props 定义
interface Props {
  actions: ActionItem[];
  record?: any;
  trigger?: 'click' | 'hover' | 'focus';
  placement?: 'top' | 'left' | 'right' | 'bottom' | 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight' | 'leftTop' | 'leftBottom' | 'rightTop' | 'rightBottom';
  layout?: 'horizontal' | 'vertical';
  maxWidth?: string;
  size?: 'small' | 'middle' | 'large';
  overlayClassName?: string;
  getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;
}

const props = withDefaults(defineProps<Props>(), {
  trigger: 'click',
  placement: 'bottomRight',
  layout: 'vertical',
  maxWidth: '200px',
  size: 'small',
  overlayClassName: 'action-popover-overlay'
});

// 计算可见的操作项
const visibleActions = computed(() => {
  return props.actions.filter(action => {
    if (action.type === 'divider') return true;
    
    if (typeof action.visible === 'function') {
      return action.visible(props.record);
    }
    return action.visible !== false;
  });
});

// 计算操作项是否禁用
const getDisabled = (action: ActionItem) => {
  if (typeof action.disabled === 'function') {
    return action.disabled(props.record);
  }
  return action.disabled || false;
};

// 处理操作点击
const handleActionClick = async (action: ActionItem) => {
  if (getDisabled(action)) return;

  // 如果需要确认
  if (action.confirm) {
    Modal.confirm({
      title: action.confirm.title,
      content: action.confirm.content,
      okText: action.confirm.okText || '确定',
      cancelText: action.confirm.cancelText || '取消',
      onOk: async () => {
        try {
          await action.onClick(props.record, action.key);
        } catch (error) {
          console.error('操作执行失败:', error);
          message.error('操作失败，请重试');
        }
      }
    });
  } else {
    try {
      await action.onClick(props.record, action.key);
    } catch (error) {
      console.error('操作执行失败:', error);
      message.error('操作失败，请重试');
    }
  }
};
</script>

<style scoped lang="less">
.action-popover-content {
  padding: 4px;
  
  &.layout-horizontal {
    display: flex;
    align-items: center;
    gap: 4px;
    
    .action-item {
      white-space: nowrap;
      margin: 0;
    }
  }
  
  &.layout-vertical {
    display: flex;
    flex-direction: column;
    gap: 2px;
    
    .action-item {
      width: 100%;
      text-align: left;
      justify-content: flex-start;
      margin: 0;
    }
  }
}

.action-item {
  border: none;
  box-shadow: none;
  padding: 4px 8px;
  height: auto;
  font-size: 14px;
  
  &:hover {
    background-color: #f5f5f5;
  }
  
  &:focus {
    background-color: #f5f5f5;
  }
  
  .action-icon {
    margin-right: 6px;
    font-size: 14px;
  }
}

// 全局样式
:global(.action-popover-overlay) {
  .ant-popover-content {
    padding: 0;
  }
  
  .ant-popover-inner {
    padding: 0;
    border-radius: 6px;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  }
}
</style>
