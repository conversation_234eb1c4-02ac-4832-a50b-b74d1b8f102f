<script lang="ts" setup>
import { onMounted, ref, onBeforeUpdate,watch } from 'vue';
import { restaurantApi } from '@haierbusiness-front/apis';

import { RHotelParams } from '@haierbusiness-front/common-libs';
import { getCurrentRouter, errorModal, routerParam, getCurrentRoute } from '@haierbusiness-front/utils';

const router = getCurrentRouter();

// 搜索弹窗相关
const showPop = ref<boolean>(false)
const popLoading = ref<boolean>(false)
const popFinished =  ref<boolean>(false)
const popHotelList = ref([])
const popHotelTotal = ref<number>(0)
const popParams =  ref<RHotelParams>({
  pageNum: 0,
  pageSize: 20,
  keyword: null, // 关键字查询
})

const popSearch = () => {
  popParams.value.pageNum= 0;
  popHotelList.value = []
  loadPopHotelList()
}
const businessList = import.meta.env.VITE_BUSINESSTRAVEL_URL;

const loadPopHotelList = () => {
  if(!popParams.value.keyword) {
    popParams.value.keyword = null
  }
  popParams.value.pageNum++;
  restaurantApi.hotelList(popParams.value).then((res) => {
    // 加载状态结束
    popLoading.value = false;
    popHotelList.value = [...popHotelList.value, ...res.data];
    popHotelTotal.value = res.total ?? 0;
    // 数据全部加载完成
    if (popHotelList.value.length >= popHotelTotal.value) {
      popFinished.value = true;
    }
  });
}

const goToDetail = (id: string) => {
  router.push({ path: '/restaurant/hotelDetail', query: { hotelId: id } });
};
const getImgUrl = (imgList) => {
  const food = []
    const restaurant = []
    const room = []
  if (imgList && imgList.length > 0) {
    
    // var type: Short? = null  //0：普通图片； 1：大堂；  2： 外景； 3： 餐厅； 4： 客房
    imgList?.forEach((item) => {
      switch (item.type) {
        case 0:
          food.push(item);
          break;
        case 1:
          room.push(item);
          break;
        case 2:
          restaurant.push(item);
          break;
        case 3:
          room.push(item);
          break;
        case 4:
          room.push(item);
          break;

        default:
          break;
      }
    });
  }
  if (restaurant.length > 0) {
    return `${businessList}api/common/v1/file/download/${restaurant[0].id}`
  } else if (room.length > 0) {
    return `${businessList}api/common/v1/file/download/${room[0].id}`

  }else if(food.length >0) {
    return `${businessList}api/common/v1/file/download/${food[0].id}`

  }else {
    return ''
  }
}

interface cateType {
  id: string;
  name: string;
}
const getFoodTypeStr = (list: Array<cateType>) => {
  if (list && list.length > 0) {
    return list.map((item) => item.name).join(' / ');
  }
};

</script>

<template>
  <!-- 搜索弹窗 -->
  <div class="book-list" style=" margin:0; width: 100vw; height:100vh ">


    <van-search v-model="popParams.keyword" shape="round" autocomplete="off" placeholder="请输入酒店名称/菜系/区域" @search="popSearch"
      class="top-search">
      <template #left-icon>
        <van-icon name="search" class="search-icon-color" color="#2681FF" @click="popSearch" />
      </template>
      <template #right-icon>
        <van-button class="search-btn font-size-vant ml-10" size="small" @click="popSearch" round>搜索</van-button>
      </template>
    </van-search>

    <van-list v-model:loading="popLoading" :finished="popFinished" :finished-text="popHotelList.length ? '没有更多了' : ''"
      @load="loadPopHotelList" :immediate-check="false">

      <van-cell v-for="(item, index) in popHotelList" :key="index" @click="goToDetail(item.id)">
        <div class="flex align-items-center">
          <div class="hotel-img mr-10">
            <img style="width: 100%; height:100%;;" :src="getImgUrl(item.album)" alt="">
          </div>
          <div class="main flex-1">
            <div>
              <van-text-ellipsis class="color-main hotel-item-title" :content="item.fullname" />
            </div>

            <van-text-ellipsis class="hotel-item-foods" :content="getFoodTypeStr(item.cateType)" />

            <van-text-ellipsis class="hotel-item-address" rows="2" :content="item.address" />

            <div>
              <van-tag class="mr-5" plain type="primary" v-for="(mark, markI) in item.landMark" v-show="markI < 2"
                :key="markI">{{ mark.name }}</van-tag>
            </div>

            <div class="hotel-item-consumptionPer">{{ `¥ ${item.consumptionPer / 100}/人` }}</div>
          </div>
        </div>
      </van-cell>
    </van-list>

    <van-empty v-if="!popLoading && popHotelList.length == 0" description="暂无数据" />

  </div>
</template>

<style lang='less' scoped>
@import url(./common.less);
</style>