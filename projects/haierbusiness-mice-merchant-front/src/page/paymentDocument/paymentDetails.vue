<!-- 付款凭证详情页 -->
<script setup lang="ts">
import {
  Button as hButton,
  Col as hCol,
  Row as hRow,
  Table as hTable,
  Upload as hUpload,
  Modal,
  message,
  Input as hInput,
  DatePicker as hDatePicker,
  Radio as hRadio,
  RadioGroup as hRadioGroup,
  Form as hForm,
  FormItem as hFormItem,
  Select as hSelect,
  SelectOption as hSelectOption,
  InputNumber as hInputNumber,
} from 'ant-design-vue';
import { ColumnType } from 'ant-design-vue/lib/table/interface';
import { PlusOutlined, DeleteOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { computed, ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { resolveParam, routerParam } from '@haierbusiness-front/utils';
import { paymentFromApi, fileApi } from '@haierbusiness-front/apis';
import type { FormInstance } from 'ant-design-vue';

const route = useRoute();
const router = useRouter();

// 接收参数
const routeParams = resolveParam(route.query.record as string);
const paymentId = ref(routeParams?.paymentId);
const paymentCode = ref(routeParams?.paymentCode);

// 页面数据
const paymentDetail = ref<any>(null);
const loading = ref(false);
const uploadLoading = ref(false);
const baseUrl = import.meta.env.VITE_BUSINESS_URL;

// 申请文件类型选择
const fileTypeOption = ref('agree');

// 添加发票相关
const invoiceModalVisible = ref(false);
const invoiceFormRef = ref<FormInstance>();
const invoiceForm = ref({
  invoiceType: 1,
  invoiceDate: '',
  invoiceNumber: '',
  invoiceAmount: undefined as number | undefined,
});

// 表单验证规则
const invoiceFormRules = {
  invoiceType: [{ required: true, message: '请选择发票类型', trigger: 'change' }],
  invoiceDate: [{ required: true, message: '请选择发票日期', trigger: 'change' }],
  invoiceNumber: [
    { required: true, message: '请输入发票号', trigger: 'blur' },
    { max: 50, message: '发票号长度不能超过50个字符', trigger: 'blur' },
  ],
  invoiceAmount: [
    { required: true, message: '请输入发票金额', trigger: 'blur' },
    { type: 'number', min: 0, message: '发票金额必须大于0', trigger: 'blur' },
  ],
};

// 发票相关 - 从API数据获取
const invoiceList = computed(() => {
  if (!paymentDetail.value?.miceInvoiceQueryDetails) {
    return [];
  }
  return paymentDetail.value.miceInvoiceQueryDetails.map((item: any, index: number) => ({
    id: item.id || index + 1,
    serialNumber: index + 1,
    invoiceNumber: item.invoiceNumber || '',
    invoiceDate: item.invoiceDate || '',
    invoiceAmount: item.invoiceAmount || 0,
    file: null, // 文件需要从attachmentFiles中匹配
  }));
});

// 模拟付款详情数据
const mockPaymentDetail = {
  paymentCode: 'D3EC20250212132380004',
  merchantName: '国海商务会议服务有限公司',
  totalAmount: 2400,
  paymentRecordsDetails: [
    {
      meetingCode: 'MC2024110171919180001',
      meetingTime: '2024-11-29 至 2024-12-01',
      manager: '张三',
      meetingAmount: 1000,
      paymentRate: 80,
      paymentAmount: 800,
    },
    {
      meetingCode: 'MC2024110171919180002',
      meetingTime: '2024-11-29 至 2024-12-01',
      manager: '张三',
      meetingAmount: 1000,
      paymentRate: 80,
      paymentAmount: 800,
    },
    {
      meetingCode: 'MC2024110171919180003',
      meetingTime: '2024-11-29 至 2024-12-01',
      manager: '张三',
      meetingAmount: 1000,
      paymentRate: 80,
      paymentAmount: 800,
    },
  ],
};

onMounted(() => {
  // 如果有 paymentId，调用详情接口
  if (paymentId.value) {
    fetchPaymentDetail();
  } else {
    // 没有传入 id 时使用模拟数据
    paymentDetail.value = mockPaymentDetail;
  }
});

// 获取付款详情
const fetchPaymentDetail = async () => {
  try {
    loading.value = true;
    
    // 调用真实的 API 接口
    const response = await paymentFromApi.getMerchantDetails(paymentId.value);
    paymentDetail.value = response;
    
    loading.value = false;
  } catch (error) {
    message.error('获取详情失败');
    loading.value = false;
  }
};

// 账单详情表格列
const billColumns: ColumnType[] = [
  {
    title: '会议单号',
    dataIndex: 'mainCode',
    width: '200px',
    align: 'center',
  },
  {
    title: '会议时间',
    dataIndex: '',
    width: '200px',
    align: 'center',
    customRender: ({ record }) => {
      if (record.startDate && record.endDate) {
        return `${record.startDate} 至 ${record.endDate}`;
      }
      return '';
    },
  },
  {
    title: '会议负责人',
    dataIndex: 'operatorName',
    width: '120px',
    align: 'center',
  },
  {
    title: '账单金额',
    dataIndex: 'billAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
  {
    title: '服务费率',
    dataIndex: 'feeRate',
    width: '100px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}%` : ''),
  },
  {
    title: '付款金额',
    dataIndex: 'paymentAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => (text != null ? `${text}元` : ''),
  },
];

// 发票表格列
const invoiceColumns: ColumnType[] = [
  {
    title: '序号',
    dataIndex: 'serialNumber',
    width: '80px',
    align: 'center',
  },
  {
    title: '发票号',
    dataIndex: 'invoiceNumber',
    width: '150px',
    align: 'center',
  },
  {
    title: '过票日期',
    dataIndex: 'invoiceDate',
    width: '120px',
    align: 'center',
  },
  {
    title: '发票金额',
    dataIndex: 'invoiceAmount',
    width: '120px',
    align: 'center',
    customRender: ({ text }) => `${text}元`,
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: '100px',
    align: 'center',
  },
];

// 计算发票总金额
const totalInvoiceAmount = computed(() => {
  const total = invoiceList.value.reduce((sum: number, item: any) => sum + (item.invoiceAmount || 0), 0);
  return total.toFixed(2);
});

// 添加发票
const addInvoice = () => {
  invoiceModalVisible.value = true;
  // 重置表单
  invoiceForm.value = {
    invoiceType: 1,
    invoiceDate: '',
    invoiceNumber: '',
    invoiceAmount: undefined,
  };
  invoiceFormRef.value?.resetFields();
};

// 提交添加发票
const handleAddInvoice = async () => {
  try {
    await invoiceFormRef.value?.validateFields();
    
    if (!paymentId.value || !paymentCode.value) {
      message.error('缺少付款单信息');
      return;
    }
    
    loading.value = true;
    
    // 调用添加发票API
    await paymentFromApi.uploadPaymentInvoice({
      paymentId: paymentId.value,
      paymentCode: paymentCode.value,
      invoiceType: invoiceForm.value.invoiceType,
      invoiceDate: invoiceForm.value.invoiceDate,
      invoiceNumber: invoiceForm.value.invoiceNumber,
      invoiceAmount: invoiceForm.value.invoiceAmount || 0,
    });
    
    message.success('添加发票成功');
    invoiceModalVisible.value = false;
    
    // 刷新详情数据
    await fetchPaymentDetail();
  } catch (error: any) {
    if (error.errorFields) {
      // 表单验证错误
      return;
    }
    message.error('添加发票失败');
  } finally {
    loading.value = false;
  }
};

// 删除发票 - 需要调用API
const deleteInvoice = (record: any) => {
  // 这里需要调用API删除发票，暂时不实现
  message.info('删除发票功能需要调用API实现');
};

// 文件上传处理
const handleUpload = (record: any, options: any) => {
  uploadLoading.value = true;

  const formData = new FormData();
  formData.append('file', options.file);

  // 模拟上传成功
  setTimeout(() => {
    record.file = {
      name: options.file.name,
      path: `/files/${options.file.name}`,
    };
    options.onSuccess();
    uploadLoading.value = false;
    message.success('文件上传成功');
  }, 1000);

  // 真实上传接口
  // fileApi.upload(formData)
  //   .then((response) => {
  //     record.file = {
  //       name: options.file.name,
  //       path: baseUrl + response.path,
  //     };
  //     options.onSuccess(response);
  //     message.success('文件上传成功');
  //   })
  //   .catch((error) => {
  //     options.onError(error);
  //     message.error('文件上传失败');
  //   })
  //   .finally(() => {
  //     uploadLoading.value = false;
  //   });
};

// 提交
const handleSubmit = () => {
  // 打印审批意见的值
  console.log('审批意见的值:', fileTypeOption.value);
  
  loading.value = true;

  // 真实提交接口 - 需要根据实际API接口调整
  const submitData = {
    paymentId: paymentId.value,
    approval: fileTypeOption.value === 'agree' ? 1 : 2, // 1通过 2驳回
  };

  // 注意：这里需要使用正确的API方法，uploadPaymentInvoice可能不是正确的方法
  // 应该使用类似 submitApproval 或其他审批相关的API
  paymentFromApi.uploadPaymentInvoice({
    paymentCode: paymentCode.value,
    invoiceType: 1, // 默认值
    invoiceDate: new Date().toISOString().split('T')[0],
    invoiceNumber: '',
    invoiceAmount: 0,
    ...submitData
  } as any)
    .then(() => {
      message.success('提交成功');
      router.push('/mice-merchant/paymentDocument/paymentOrderList');
    })
    .catch(() => {
      message.error('提交失败');
    })
    .finally(() => {
      loading.value = false;
    });
};

// 返回
const goBack = () => {
  router.push('/mice-merchant/paymentDocument/paymentOrderList');
};
</script>

<template>
  <div class="payment-details-container">
    <h-row :gutter="24" style="height: 100%">
      <!-- 左侧：账单详情 -->
      <h-col :span="12" class="left-panel">
        <div class="panel-header">
          <h3>付款单详情</h3>
        </div>

        <div class="detail-info">
          <div class="info-item">
            <span class="label">付款单号：</span>
            <span class="value">{{ paymentDetail?.paymentCode }}</span>
          </div>
          <div class="info-item">
            <span class="label">供应商：</span>
            <span class="value">{{ paymentDetail?.merchantName }}</span>
          </div>
          <div class="info-item">
            <span class="label">收款总金额：</span>
            <span class="value amount">{{ paymentDetail?.totalAmount }}元</span>
          </div>
          <div class="info-item" v-if="paymentId">
            <span class="label">付款ID：</span>
            <span class="value">{{ paymentId }}</span>
          </div>
        </div>
        <h-table
          :columns="billColumns"
          :data-source="paymentDetail?.paymentRecordsDetails || []"
          :pagination="false"
          size="small"
          bordered
          :scroll="{ y: 300 }"
        />
      </h-col>

      <!-- 右侧：发票上传 -->
      <h-col :span="12" class="right-panel">
        <div class="panel-header">
          <h3>申请单发票上传</h3>
        </div>

        <div class="upload-section">
          <div class="file-type">
            <span class="label">审批意见：</span>
            <h-radio-group v-model:value="fileTypeOption" class="file-type-radio">
              <h-radio value="agree">通过</h-radio>
              <h-radio value="reject">驳回</h-radio>
            </h-radio-group>
          </div>

          <h-table
            :columns="invoiceColumns"
            :data-source="invoiceList"
            :pagination="false"
            size="small"
            bordered
            :scroll="{ y: 300 }"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'invoiceNumber'">
                <span>{{ record.invoiceNumber }}</span>
              </template>

              <template v-if="column.dataIndex === 'invoiceDate'">
                <span>{{ record.invoiceDate }}</span>
              </template>

              <template v-if="column.dataIndex === 'invoiceAmount'">
                <span>{{ record.invoiceAmount }}元</span>
              </template>

              <template v-if="column.dataIndex === 'action'">
                <div class="action-buttons">
                  <h-button
                    size="small"
                    type="link"
                    danger
                    @click="deleteInvoice(record)"
                    :disabled="invoiceList.length <= 1"
                  >
                    <DeleteOutlined />
                    删除
                  </h-button>
                </div>
              </template>
            </template>
          </h-table>

          <div class="add-invoice-section">
            <h-button type="dashed" @click="addInvoice" class="add-invoice-btn">
              <PlusOutlined />
              添加发票
            </h-button>
          </div>

          <div class="total-section">
            <span class="total-label">合计：</span>
            <span class="total-amount">{{ totalInvoiceAmount }}元</span>
          </div>
        </div>

        <div class="footer-actions">
          <h-button @click="goBack" style="margin-right: 12px"> 返回 </h-button>
          <h-button type="primary" @click="handleSubmit" :loading="loading"> 提交 </h-button>
        </div>
      </h-col>
    </h-row>
    
    <!-- 添加发票模态框 -->
    <Modal
      v-model:open="invoiceModalVisible"
      title="添加发票"
      :footer="null"
      @cancel="invoiceModalVisible = false"
      width="500px"
    >
      <div class="modal-content">
        <div class="invoice-form-item">
          <label class="invoice-form-label">发票类型：</label>
          <h-select v-model:value="invoiceForm.invoiceType" class="invoice-form-input">
            <h-select-option :value="1">国旅</h-select-option>
            <h-select-option :value="2">服务商</h-select-option>
          </h-select>
        </div>

        <div class="invoice-form-item">
          <label class="invoice-form-label">发票日期：<span class="required-mark">*</span></label>
          <hDatePicker
            v-model:value="invoiceForm.invoiceDate"
            class="invoice-form-input"
            placeholder="请选择发票日期"
            value-format="YYYY-MM-DD"
          />
        </div>

        <div class="invoice-form-item">
          <label class="invoice-form-label">发票号：<span class="required-mark">*</span></label>
          <hInput
            v-model:value="invoiceForm.invoiceNumber"
            placeholder="请输入发票号"
            class="invoice-form-input"
            :maxlength="50"
          />
        </div>

        <div class="invoice-form-item">
          <label class="invoice-form-label">发票金额：<span class="required-mark">*</span></label>
          <hInputNumber
            v-model:value="invoiceForm.invoiceAmount"
            placeholder="请输入发票金额"
            class="invoice-form-input"
            :min="0"
            :precision="2"
          />
        </div>

        <div class="modal-footer">
          <hButton class="button-margin" @click="invoiceModalVisible = false">取消</hButton>
          <hButton type="primary" @click="handleAddInvoice" :loading="loading">确定</hButton>
        </div>
      </div>
    </Modal>
  </div>
</template>

<style scoped lang="less">
.payment-details-container {
  height: 100vh;
  padding: 16px;
  background-color: #f5f5f5;
}

.left-panel,
.right-panel {
  height: 100%;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.panel-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 12px;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}

.detail-info {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    font-weight: 500;
    color: #666;
    width: 100px;
    flex-shrink: 0;
  }

  .value {
    color: #333;

    &.amount {
      color: #f56a00;
      font-weight: 600;
    }
  }
}

.upload-section {
  margin-bottom: 20px;
}

.section-title {
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.file-type {
  margin-bottom: 16px;
  display: flex;
  align-items: center;

  .label {
    font-weight: 500;
    color: #666;
    margin-right: 12px;
  }

  .value {
    color: #333;
    margin-left: 8px;
  }

  .file-type-radio {
    margin-left: 0;
  }
}

.action-buttons {
  display: flex;
  flex-direction: row;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.add-invoice-section {
  margin: 16px 0;
  text-align: center;
}

.add-invoice-btn {
  width: 100%;
  height: 40px;
  border-style: dashed;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
  }
}

.total-section {
  margin: 16px 0;
  text-align: right;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;

  .total-label {
    font-weight: 500;
    color: #666;
  }

  .total-amount {
    font-weight: 600;
    color: #f56a00;
    font-size: 16px;
  }
}

.footer-actions {
  margin-top: 20px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e8e8e8;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 8px !important;
}

:deep(.ant-input),
:deep(.ant-picker) {
  border-radius: 4px;
}

// 弹框内容样式
.modal-content {
  padding: 20px 0;
}

.modal-footer {
  text-align: right;
  margin-top: 20px;
}

// 发票表单样式
.invoice-form-item {
  margin-bottom: 16px;
}

.invoice-form-label {
  font-weight: bold;
}

.invoice-form-input {
  width: 100%;
  margin-top: 8px;
}

.required-mark {
  color: red;
}

.button-margin {
  margin-right: 10px;
}
</style>
