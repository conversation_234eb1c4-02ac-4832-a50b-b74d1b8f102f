<script lang="ts" setup>
import { ref, onMounted, useAttrs, inject, watch, watchEffect, provide, reactive } from 'vue';
import { Modal, message } from 'ant-design-vue';
import Scheme from '@haierbusiness-front/components/mice/scheme/component/index.vue';
import { useRouter, useRoute } from 'vue-router';
import { getDealTime, routerParam, resolveParam } from '@haierbusiness-front/utils';
import finalNode from '@haierbusiness-front/components/mice/orderList/finalNode.vue';
const router = useRouter();
const route = useRoute();
const footerChecked = ref('b');
const isCloseLastTab = inject<Ref<boolean>>('isCloseLastTab');
const scheme = ref(null);
const btnLoading = ref(true);
// 方案视图
const handleSchemeView = (type) => {
  let query = {
    ...route.query,
    type: type,
  };
  if (route.path === '/bidman/scheme/index') router.push({ path: '/bidman/scheme/view', query: query });
  else if (route.path === '/bidman/scheme/confirm') {
    if (resolveParam(route.query.record).orderType === 'detail') {
      if (type === 'a') router.push({ path: '/bidman/scheme/confirm/view', query: query });
      else if (type !== footerChecked.value && ['b', 'c'].includes(type)) {
        router.push({ path: '/bidman/scheme/confirm', query: query });
        setTimeout(() => {
          scheme.value?.getList();
          // isCloseLastTab.value = true;
        }, 1000);
      }
      // isCloseLastTab.value = true;
      return;
    }
    router.push({ path: '/bidman/scheme/confirm/view', query: query });
  } else if (route.path === '/bidman/bid/index') {
    router.push({ path: '/bidman/bid/view', query: query });
  }
  // isCloseLastTab.value = true;
};

const saveSchemeH = () => {
  if (route.path === '/bidman/scheme/confirm') {
    scheme.value.saveSchemeConfirm();
  } else if (route.path === '/bidman/scheme/index') {
    Modal.confirm({
      title: '方案审核',
      content: '是否确认提交？',
      onOk() {
        scheme.value.saveScheme();
      },
      onCancel() {
        console.log('Cancel');
      },
      class: 'test',
    });
  } else if (route.path === '/bidman/bid/index') {
    scheme.value.saveScheme();
  }
};
const showBtnT = ref(true);
const showBtn = () => {
  let text = '';
  if (route.path === '/bidman/scheme/index') {
    text = '提交';
  } else if (route.path === '/bidman/scheme/confirm') text = '方案确认';
  else if (route.path === '/bidman/bid/index') text = '发布竞价';
  return text;
};

const hideBtn = ref('0');
const frameModel = ref(inject<any>('frameModel'));
const $attrs = useAttrs();
const backupOrderList = () => {
  if ($attrs.orderSource === 'manage') {
    router.push('/bidman/orderList/index');
    // 关闭当前页签
    isCloseLastTab.value = true;
  } else {
    const localUrl = window.location.href;

    const businessMiceBid = import.meta.env.VITE_BUSINESS_INDEX_URL + '#';

    // 跳转需求确认页面
    const url = businessMiceBid + '/card-order/miceOrder?record=' + routerParam(resolveParam(route.query.record));
    window.location.href = url;
    return;
  }
};

const arrConfirmView = ref([
  'COST_APPROVAL',
  'MICE_PENDING',
  'MICE_EXECUTION',
  'MICE_COMPLETED',
  'BILL_CONFIRM',
  'BILL_APPROVAL',
  'BILL_RE_APPROVAL',
  'PAYMENT_CONFIRM',
  'PLATFORM_INVOICE_ENTRY',
  'VENDOR_INVOICE_ENTRY',
  'INVOICE_CONFIRM',
  'PLATFORM_REFUND_RECEIPT_UPLOAD',
  'REFUND_CONFIRM',
  'PLATFORM_PAY_RECEIPT_UPLOAD',
  'PLATFORM_INVOICE_CONFIRM',
  'SETTLEMENT_PENDING',
  'SETTLEMENT_RECORDED',
  'END',
]);
const schemeSumInitiate = ref([]);
provide('schemeSumInitiate', schemeSumInitiate);
watch(
  schemeSumInitiate,
  (newVal) => {
    if (newVal) {
      newVal.forEach((item) => {
        if (!item.isExclude) showBtnT.value = false;
      });
    }
    if (newVal.length === 0) showBtnT.value = true;
    btnLoading.value = false;
  },
  { deep: true },
);
const record = reactive({});

onMounted(() => {
  if (route.query.record.includes('%')) Object.assign(record, resolveParam(route.query.record));
  else {
    if (typeof route.query.record == 'string') Object.assign(record, JSON.parse(route.query.record));
  }
  footerChecked.value = route.query.type || 'b';
  hideBtn.value = record?.hideBtn || '0';
  frameModel.value = hideBtn.value === '1' ? 1 : 0;
});
</script>

<template>
  <div class="container">
    <Scheme v-bind="$attrs" ref="scheme" :class="record.hideBtn == '1' ? 'footer-user-width' : ''">
      <!-- 底部插槽：操作按钮组 -->
      <template #footer v-if="!btnLoading">
        <a-radio-group v-model:value="footerChecked" button-style="solid">
          <a-radio-button @click="handleSchemeView('a')" value="a">需求视图</a-radio-button>
          <a-radio-button @click="handleSchemeView('b')" value="b">方案视图</a-radio-button>
          <a-radio-button
            v-if="
              ['BID_RESULT_CONFIRM'].concat(arrConfirmView).includes(scheme?.miceDetail?.processNode) &&
              resolveParam(route.query.record).orderType === 'detail'
            "
            @click="handleSchemeView('c')"
            value="c"
            >竞价视图</a-radio-button
          >
        </a-radio-group>
        <a-button
          v-if="
            (hideBtn !== '1' && ['/bidman/scheme/confirm'].includes(route.path) && $attrs.orderSource === 'manage') ||
            resolveParam(route.query.record).orderType === 'detail' ||
            (showBtnT && ['SCHEME_APPROVAL'].includes(scheme?.miceDetail?.processNode))
          "
          style="float: right"
          @click="backupOrderList()"
          type="primary"
          >返回</a-button
        >
        <a-button v-show="hideBtn !== '1'" v-else style="float: right" type="primary" @click="saveSchemeH">{{
          showBtn()
        }}</a-button>
      </template>
    </Scheme>
  </div>
</template>
<style lang="less" scoped>
.container {
  background: #f1f2f6;
  padding: 0 auto;
}
.footer-user-width {
  width: 1280px !important;
  left: calc(50% - 640px);
}
</style>
