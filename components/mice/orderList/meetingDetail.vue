<script setup lang="ts">
import { Button as hButton, Modal as hModal } from 'ant-design-vue';
// 会议详情 - 组件
name: 'meetingDetail';
import { ref, onMounted, reactive, onUnmounted, defineProps, computed } from 'vue';
import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import { miceBidManOrderListApi } from '@haierbusiness-front/apis';
import { CardItem } from '@haierbusiness-front/common-libs';
import { useRoute, useRouter } from 'vue-router';
import { resolveParam } from '@haierbusiness-front/utils';
import finalNode from '@haierbusiness-front/components/mice/orderList/finalNode.vue';
import orderLog from '@haierbusiness-front/components/mice/orderList/orderLog.vue';
import memorandum from '@haierbusiness-front/components/mice/orderList/memorandum.vue';

import advisors from './../advisors/index.vue';
import { applicationStore } from '@haierbusiness-front/utils/src/store/applicaiton';

const route = useRoute();
const router = useRouter();
const store = applicationStore();
const props = defineProps({
  type: {
    type: String,
    default: 'manage',
    // manage - 平台-订单列表
    // user - 用户端-订单列表
  },
  hideUserBtn: {
    // 用户端-订单列表详情-隐藏用户端按钮
    type: Boolean,
    default: false,
  },
});

const previewSource = ref<string>('demandOne');
const showProcess = ref<boolean>(false);
const visible = ref<boolean>(false);
const memorandumvisible = ref<boolean>(false);
const confirmLoading = ref(false);
const handleOk = (num) => {
  if (num == 1) {
    visible.value = false;
  } else {
    memorandumvisible.value = false;
  }
};

const isViewLog = ref(false);

const permission = () => {
  store.loginUser.authorities.forEach((item) => {
    if (
      item.authority == import.meta.env.VITE_BUSINESS_CONSULTANT ||
      item.authority == import.meta.env.VITE_BUSINESS_MANAGE
    ) {
      isViewLog.value = true;
    }
  });
};

const hideBtn = ref<string>('');

// 查看流程节点
const viewProcess = () => {
  showProcess.value = !showProcess.value;
};

// 备忘录
const viewMemo = () => {
  memorandumvisible.value = true;
};
// 日志
const viewLog = () => {
  visible.value = true;
};
// 驳回
const rejectBtn = () => {
  console.log('%c [ 驳回 ]-161', 'font-size:13px; background:pink; color:#bf2c9f;');
};
// 确认
const confirmBtn = () => {
  console.log('%c [ 确认 ]-161', 'font-size:13px; background:pink; color:#bf2c9f;');
};
// 返回
const backBtn = () => {
  router.go(-1);
};
onMounted(() => {
  const record = resolveParam(route.query.record);
  permission();
  hideBtn.value = record.hideBtn || '';
});
</script>
<template>
  <div class="container">
    <!-- 会议详情 -->
    <!-- 详情 -->
    <div class="">
      <advisors
        :preview-source="previewSource"
        :is-manage-page="type === 'manage'"
        :platformType="type"
        :hide-footer="true"
      >
        <template #header>
          <div v-if="hideBtn !== '1'">
            <a-button class="mr10" size="small" type="link" @click="viewProcess()">
              {{ showProcess ? '收起流程节点' : '查看流程节点' }}
              <UpOutlined v-if="!showProcess" />
              <DownOutlined v-else />
            </a-button>
            <a-button
              style="margin-right: 10px"
              size="small"
              @click="viewMemo()"
              v-if="isViewLog && (type === 'manage' || hideUserBtn)"
              >备忘录</a-button
            >
            <a-button
              v-if="isViewLog && (type == 'manage' || hideUserBtn)"
              type="primary"
              size="small"
              @click="viewLog()"
              >日志</a-button
            >
          </div>
        </template>
        <template #processSlot>
          <!-- 流程信息 -->
          <div class="" v-if="showProcess">
            <finalNode :nodeId="route.query.record" :key="route.fullPath"></finalNode>
          </div>
        </template>
        <template #orderLogSlot> </template>
        <template #footer>
          <div v-if="hideBtn !== '1'" style="display: inline-block">
            <a-button style="margin-right: 10px" size="small" @click="backBtn()">返回</a-button>
            <!-- <a-button style="margin-right: 10px" size="small" @click="rejectBtn()">驳回</a-button>
            <a-button type="primary" size="small" @click="confirmBtn()">确认</a-button> -->
          </div>
        </template>
      </advisors>
    </div>
    <a-modal
      :open="visible"
      width="70%"
      :confirmLoading="confirmLoading"
      @ok="handleOk(1)"
      @cancel="visible = false"
      title="日志"
      centered
    >
      <orderLog :nodeId="route.query.record" />
      <template #footer>
        <h-button @click="visible = false">关闭</h-button>
      </template>
    </a-modal>
    <a-modal
      :open="memorandumvisible"
      width="70%"
      :confirmLoading="confirmLoading"
      @ok="handleOk(2)"
      @cancel="memorandumvisible = false"
      title="备忘录"
      centered
    >
      <memorandum :nodeId="route.query.record" />
      <template #footer>
        <h-button @click="memorandumvisible = false">关闭</h-button>
      </template>
    </a-modal>
  </div>
</template>
<style scoped lang="less">
.container {
  padding-top: 16px;
  background: #ffffff;
  width: 100%;
  height: 100%;

  ::v-deep .ant-divider-inner-text {
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
    font-family: PingFangSC-regular;
  }

  ::v-deep .ant-radio-button-wrapper-checked {
    background: #409eff;
    color: white;
  }
}

.btn1 {
  padding-left: 40px;
}

.vertical {
  display: flex;
  align-items: center;
}

.rowFlex {
  display: flex;
  justify-content: space-between;
}

.card {
  width: 100%;
  height: 120px;
  padding: 10px 20px;
}

.cardItem {
  border: 1px solid #e9e9e9;
  padding: 10px;
  width: 200px;
  height: 100px;
}

.cardItemTitle {
  height: 30px;
  color: rgba(14, 6, 6, 0.85);
  font-size: 14px;
  font-family: PingFangSC-regular;
}

.cardItemCenter {
  height: 25px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  font-family: PingFangSC-regular;
}

.cardItemBottom {
  height: 25px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  font-family: PingFangSC-regular;

  ::v-deep .ant-btn {
    height: 20px;
    display: flex;
    align-items: center;
  }
}

.btn2 {
  text-align: right;
  height: 20px;
  width: 100%;
  color: rgba(0, 0, 0, 0.85);
  font-size: 10px;
  font-family: PingFangSC-regular;
}

.img1 {
  margin: 0 5px;
  display: inline-block;
  width: 10px;
  height: 10px;
}
:where(.css-dev-only-do-not-override-bq26c2).ant-btn > span + .anticon {
  margin-inline-start: 2px;
}
</style>
