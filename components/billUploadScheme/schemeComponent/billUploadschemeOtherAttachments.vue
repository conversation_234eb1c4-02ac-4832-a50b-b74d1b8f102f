<script setup lang="ts">
// 其他附件组件
import { message } from 'ant-design-vue';
import { ref, defineProps, defineEmits, computed, watch } from 'vue';
import { fileApi } from '@haierbusiness-front/apis';
import arrow from '@/assets/image/orderList/delete.png'; //删除
import deleteGray from '@/assets/image/orderList/deleteGray.png'; //删除灰色
import add from '@/assets/image/orderList/add.png'; //添加
import { UploadFile, OtherAttachmentItem, BILL_UPLOAD_FILE_CONFIG } from '@haierbusiness-front/common-libs';
import { UploadOutlined } from '@ant-design/icons-vue';

// 注意：OtherAttachmentItem 类型定义已移至 @haierbusiness-front/common-libs/micemerchant

//鼠标移入效果
const hoverColor = ref(-1)

// 文件上传相关常量（从 common-libs 导入，使用扩展文件类型）
const { EXTENDED_FILE_TYPES: SUPPORTED_FILE_TYPES, EXTENDED_FILE_SIZE_LIMIT: FILE_SIZE_LIMIT, EXTENDED_UPLOAD_ACCEPT: UPLOAD_ACCEPT } = BILL_UPLOAD_FILE_CONFIG;

const props = defineProps({
  otherAttachmentList: {
    type: Array as () => OtherAttachmentItem[],
    default: () => [],
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  schemeDetail: {
    type: Object as () => any,
    default: () => ({}),
  },
});

const emit = defineEmits(['otherAttachmentsEmit', 'otherAttachmentDeleted']);

// 响应式数据
const previewVisible = ref(false);
const previewFile = ref<UploadFile | null>(null);
const previewFileName = ref('');

// 用于存储每个条目的上传状态
const itemUploadingState = ref<Record<string, boolean>>({});

// 获取基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL;
// 🔥 计算属性：获取要显示的其他附件数据
const displayOtherAttachmentList = computed(() => {
  if (props.readonly && props.schemeDetail?.attachmentOthers) {
    // 查看模式：从 schemeDetail.attachmentOthers 获取数据并转换格式
    const transformedData = props.schemeDetail.attachmentOthers.map((item: any, index: number) => {
      // 🔥 修复：将 paths 转换为 files 格式
      const files = (item.paths || []).map((path: string, pathIndex: number) => {
        // 处理路径，确保正确的 URL 格式
        let processedPath = path;

        // 如果路径已经包含完整 URL，直接使用
        if (path.startsWith('http')) {
          processedPath = path;
        } else {
          // 如果是相对路径，拼接 baseUrl
          processedPath = path.startsWith('/') ? baseUrl + path : baseUrl + '/' + path;
        }

        // 生成文件名
        const fileName = path.split('/').pop() || `其他附件${pathIndex + 1}`;

        return {
          uid: `${item.id || index}_${pathIndex}`,
          name: fileName,
          status: 'done',
          url: processedPath,
          filePath: processedPath,
        };
      });

      return {
        ...item,
        tempId: item.tempId || `view_other_${item.id || Date.now()}_${index}`,
        serialNumber: item.serialNumber ?? index + 1,
        description: item.description || item.subType || '',
        paths: Array.isArray(item.paths) ? item.paths : [item.paths].filter(Boolean),
        files: files, // 🔥 修复：使用转换后的 files
      };
    });

    return transformedData;
  } else {
    // 编辑模式：使用 otherAttachmentList
    return props.otherAttachmentList || [];
  }
});

// 获取文件显示名称
const getFileDisplayName = (fileName: string): string => {
  if (!fileName) return '';

  if (fileName.includes('-')) fileName = fileName.split('-')[1];

  const maxLength = 15;
  if (fileName.length <= maxLength) return fileName;

  const extension = fileName.split('.').pop() || '';
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';

  return `${truncatedName}.${extension}`;
};

// 文件上传前验证
const beforeUpload = (file: File): boolean => {
  const isValidType =
    SUPPORTED_FILE_TYPES.includes(file.type) ||
    file.name.toLowerCase().endsWith('.pdf') ||
    file.name.toLowerCase().endsWith('.doc') ||
    file.name.toLowerCase().endsWith('.docx') ||
    file.name.toLowerCase().endsWith('.xls') ||
    file.name.toLowerCase().endsWith('.xlsx') ||
    file.name.toLowerCase().endsWith('.ppt') ||
    file.name.toLowerCase().endsWith('.pptx') ||
    file.name.toLowerCase().endsWith('.txt');

  if (!isValidType) {
    message.error('支持上传 PDF、图片、Office文档、文本文件格式！');
    return false;
  }

  const isValidSize = file.size / 1024 / 1024 < FILE_SIZE_LIMIT;
  if (!isValidSize) {
    message.error(`文件大小不能超过 ${FILE_SIZE_LIMIT}MB！`);
    return false;
  }

  return true; // 验证通过，允许上传
};

// 新增附件分类
const handleAddAttachmentCategory = () => {
  const newItem: OtherAttachmentItem = {
    tempId: `${Date.now()}_${Math.random()}`,
    serialNumber: props.otherAttachmentList.length + 1,
    description: '',
    paths: [],
    files: [],
    _uploading: false,
  };

  const updatedList = [...props.otherAttachmentList, newItem];
  emit('otherAttachmentsEmit', updatedList);
};

// 更新附件字段
const updateAttachment = (itemId: string, field: string, value: any) => {
  const updatedList = props.otherAttachmentList.map((item) => {
    if (item.tempId === itemId) {
      return { ...item, [field]: value };
    }
    return item;
  });
  emit('otherAttachmentsEmit', updatedList);
};

// 文件上传处理
const handleFileUpload = (options: any, itemId: string) => {
  // 设置该条目的上传状态
  itemUploadingState.value[itemId] = true;

  // 更新条目的上传状态
  const updatedListForLoading = props.otherAttachmentList.map((item) => {
    if (item.tempId === itemId) {
      return { ...item, _uploading: true };
    }
    return item;
  });
  emit('otherAttachmentsEmit', updatedListForLoading);

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((response) => {
      const fileObj: UploadFile = {
        uid: options.file.uid || Date.now().toString(),
        name: options.file.name,
        status: 'done',
        url: response.path ? baseUrl + response.path : '',
        filePath: response.path ? baseUrl + response.path : '',
        fileName: options.file.name,
      };
      const updatedList = props.otherAttachmentList.map((item) => {
        if (item.tempId === itemId) {
          return {
            ...item,
            files: [...(item.files || []), fileObj],
            paths: [...(item.paths || []), response.path ? baseUrl + response.path : ''],
          };
        }
        return item;
      });

      emit('otherAttachmentsEmit', updatedList);
      message.success(`文件 ${options.file.name} 上传成功`);
    })
    .catch((error) => {
      console.error('上传失败:', error);
      message.error(`文件 ${options.file.name} 上传失败，请重试`);
    })
    .finally(() => {
      // 清除该条目的上传状态
      itemUploadingState.value[itemId] = false;

      // 更新条目状态
      const finalList = props.otherAttachmentList.map((item) => {
        if (item.tempId === itemId) {
          return { ...item, _uploading: false };
        }
        return item;
      });
      emit('otherAttachmentsEmit', finalList);
    });
};

// 删除文件 - 真正删除数据而不是隐藏
const handleRemoveFile = (file: UploadFile, itemId: string) => {
  const updatedList = props.otherAttachmentList.map((item) => {
    if (item.tempId === itemId) {
      const fileIndex = item.files.findIndex((f) => f.uid === file.uid);
      if (fileIndex > -1) {
        // 创建新的数组，真正删除对应索引的数据
        const newFiles = [...item.files];
        const newPaths = [...item.paths];

        // 从数组中移除对应索引的元素
        newFiles.splice(fileIndex, 1);
        newPaths.splice(fileIndex, 1);

        return {
          ...item,
          files: newFiles,
          paths: newPaths,
        };
      }
    }
    return item;
  });

  // 立即更新到父组件，确保数据真正被删除
  emit('otherAttachmentsEmit', updatedList);
  message.success('文件删除成功');
};

// 删除其他附件记录 - 真正删除数据而不是隐藏
const handleDeleteOtherAttachment = (itemId: string) => {
  if (props.readonly) {
    message.warning('查看模式下不能删除附件');
    return;
  }

  // 真正过滤掉要删除的附件记录
  const updatedList = props.otherAttachmentList.filter((item) => item.tempId !== itemId);

  // 重新分配序号
  const reorderedList = updatedList.map((item, index) => ({
    ...item,
    serialNumber: index + 1,
  }));

  // 通知父组件附件被删除，需要清除关联数据
  emit('otherAttachmentDeleted', itemId);

  // 立即更新附件列表，确保数据真正被删除
  emit('otherAttachmentsEmit', reorderedList);

  message.success('附件删除成功');
};

// 文件预览
const handlePreviewFile = (file: UploadFile) => {
  previewFile.value = file;
  previewFileName.value = file.name;
  previewVisible.value = true;
};

// 关闭预览
const handlePreviewCancel = () => {
  previewVisible.value = false;
  previewFile.value = null;
  previewFileName.value = '';
};

// 下载文件
const handleDownloadFile = () => {
  if (previewFile.value && previewFile.value.url) {
    window.open(previewFile.value.url, '_blank');
  }
};

// 监听 schemeDetail 变化（仅查看模式使用）
watch(
  () => props.schemeDetail,
  (newSchemeDetail) => {
    // 只在查看模式下处理 schemeDetail
    if (props.readonly && newSchemeDetail && newSchemeDetail.attachmentOthers) {
      // 查看模式：直接使用数据展示，通过 emit 传递给父组件进行显示
      emit('otherAttachmentsEmit', newSchemeDetail.attachmentOthers);
    }
  },
  { immediate: true, deep: true },
);

// 🔧 修复：监听其他附件列表变化，空数组时自动添加一条空数据
watch(
  () => props.otherAttachmentList,
  (newAttachmentList, oldAttachmentList) => {
    // 🔧 修复：只要是空数组且不是只读模式，就应该自动添加一条空数据供用户填写
    // 这样保证了首次进入和缓存回显时的一致性
    const shouldAddEmptyCategory =
      !props.readonly && (!props.otherAttachmentList || props.otherAttachmentList.length === 0);

    if (shouldAddEmptyCategory) {
      handleAddAttachmentCategory();
    }
  },
  { immediate: true, deep: true },
);
</script>

<template>
  <!-- 其他附件 -->
  <div class="scheme_other_attachments">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>其他附件</span>
    </div>

    <!-- 表格布局 -->
    <div class="info-table-wrapper other-attachments-table">
      <div class="table-header">
        <div class="col-serial font-color">序号</div>
        <div class="col-description font-color">其他附件说明</div>
        <div class="col-files font-color">附件</div>
        <div class="col-operation font-color" v-if="!readonly">操作</div>
      </div>

      <div class="table-body">
        <div v-for="(item, index) in displayOtherAttachmentList" :key="item.tempId || item.id || index"
          class="table-row">
          <!-- 序号 -->
          <div class="col-serial">
            {{ item.serialNumber }}
          </div>

          <!-- 其他附件说明 -->
          <div class="col-description">
            <!-- 查看模式：显示纯文本 -->
            <span v-if="readonly" class="readonly-text">
              {{ item.description || '-' }}
            </span>
            <!-- 编辑模式：显示输入框 -->
            <a-input v-else :value="item.description" placeholder="请输入其他附件说明" size="small" class="borderless-input"
              :bordered="false"
              @change="(e: Event) => updateAttachment(item.tempId, 'description', (e.target as HTMLInputElement).value)" />
          </div>

          <!-- 附件文件 -->
          <div class="col-files">
            <div class="files-content">
              <!-- 已上传文件标签 -->
              <div class="file-tags" v-if="item.files?.length > 0">
                <span v-for="file in item.files" :key="file.uid" class="file-tag-wrapper">
                  <a-tag class="file-tag" @click="() => handlePreviewFile(file)">
                    {{ getFileDisplayName(file.name) }}
                  </a-tag>
                  <a-popconfirm v-if="!readonly" :title="`确认删除文件 ${file.name}？`" description="删除后无法恢复" ok-text="删除"
                    cancel-text="取消" @confirm="() => handleRemoveFile(file, item.tempId)">
                    <a-button type="text" size="small" danger class="file-delete-btn">×</a-button>
                  </a-popconfirm>
                </span>
              </div>
              <div v-else-if="readonly" class="readonly-text">暂无附件</div>

              <!-- 上传按钮 - 查看模式下隐藏 -->
              <a-upload v-if="!readonly" :file-list="[]" :before-upload="beforeUpload"
                :custom-request="(options: any) => handleFileUpload(options, item.tempId)" :show-upload-list="false"
                :accept="UPLOAD_ACCEPT" multiple>
                <a-button size="small" type="link"
                  :loading="item._uploading || itemUploadingState[item.tempId] || false">
                  <upload-outlined />
                  上传附件
                </a-button>
              </a-upload>
            </div>
          </div>

          <!-- 操作列 - 仅编辑模式显示 -->
          <div class="col-operation" v-if="!readonly">
            <a-popconfirm title="确认删除该附件吗？" description="删除后将清除该附件的所有信息及关联数据" ok-text="删除" cancel-text="取消"
              @confirm="() => handleDeleteOtherAttachment(item.tempId)">
              <a-button type="link" size="small" danger>
                <img :src="hoverColor == index ? arrow : deleteGray" alt="" class="imgBig"
                  @mouseenter="hoverColor = index" @mouseleave="hoverColor = -1" />
              </a-button>
            </a-popconfirm>
          </div>
        </div>

        <!-- 添加按钮行 - 查看模式下隐藏 -->
        <div v-if="!readonly" class="table-row add-row">
          <div class="add-button-full-width" @click="handleAddAttachmentCategory">
            <div class="demand_add">
              <img :src="add" alt="" class="imgAddBig" style="margin-right: 5px" />
              <span>新增附件</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件预览弹框 -->
    <a-modal v-model:open="previewVisible" title="文件预览" :footer="null" width="80%" @cancel="handlePreviewCancel">
      <div class="preview-content">
        <div class="preview-header">
          <h4>{{ previewFileName }}</h4>
        </div>
        <div class="preview-body">
          <template v-if="previewFile && previewFile.url">
            <img v-if="
              previewFile.name &&
              (previewFile.name.toLowerCase().includes('.jpg') ||
                previewFile.name.toLowerCase().includes('.jpeg') ||
                previewFile.name.toLowerCase().includes('.png') ||
                previewFile.name.toLowerCase().includes('.gif'))
            " :src="previewFile.url" alt="预览图片" style="max-width: 100%; max-height: 500px; object-fit: contain" />
            <iframe v-else-if="previewFile.name && previewFile.name.toLowerCase().includes('.pdf')"
              :src="previewFile.url" style="width: 100%; height: 500px; border: none"></iframe>
            <div v-else class="file-download">
              <p>无法预览此文件类型，请下载查看</p>
              <a-button type="primary" @click="handleDownloadFile"> 下载文件 </a-button>
            </div>
          </template>
          <template v-else>
            <div class="no-file">
              <p>文件信息：{{ previewFileName }}</p>
              <p>暂无可预览的文件内容</p>
            </div>
          </template>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
* {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

.font-color {
  color: #86909c;
}

.imgBig {
  width: 20px;
  height: 20px;
}

.imgAddBig {
  width: 16px;
  height: 16px;
}

.scheme_other_attachments {
  position: relative;
  margin-bottom: 24px;

  .interact_title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 500;

    .interact_shu {
      width: 4px;
      height: 20px;
      background: #1868db;
      border-radius: 2px;
    }

    span {
      font-size: 18px;
      font-weight: 500;
      font-family: PingFangSC, PingFang SC;
      color: #1d2129;
    }

    .tip-text {
      margin-left: 16px;
      font-size: 12px;
      color: #ff4d4f;
      font-weight: normal;
    }
  }

  .info-table-wrapper {
    width: 100%;
    border: none;
    // border-bottom: 1px solid #d9d9d9;
    border-radius: 0;
    margin-bottom: 0;

    .table-header {
      display: flex;
      background-color: #f2f3f5;
      font-weight: 500;
      font-size: 14px;
      color: #333;

      >div {
        padding: 12px 8px;
        text-align: center;
      }

      .col-serial {
        width: 80px;
        // border-right: 1px solid #d9d9d9;
      }

      .col-description {
        width: 200px;
        // border-right: 1px solid #d9d9d9;
      }

      .col-files {
        flex: 1;
        min-width: 200px;
      }

      .col-operation {
        width: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .table-body {
      .table-row {
        display: flex;
        border-bottom: 1px solid #e5e6eb;

        &:last-child {
          border-bottom: none;
        }

        &.add-row {
          border-bottom: none;
          margin-top: 5px; // 🔧 新增：距离上边5px

          .add-button-full-width {
            width: auto; // 🔧 修改：从100%改为auto，让按钮宽度自适应内容
            padding: 8px 12px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            min-height: 38px;
            cursor: pointer;
            border: none; // 🔧 修改：去掉边框
            border-radius: 6px; // 🔧 新增：添加圆角
            background-color: transparent; // 🔧 新增：透明背景
            transition: all 0.3s ease; // 🔧 新增：添加过渡效果

            &:hover {
              background-color: #f5f5f5; // 🔧 保留：悬停背景色
            }

            .demand_add {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              color: #1890ff;
              font-size: 14px;
              margin-left: 8px;

              .demand_add_img {
                width: 16px;
                height: 16px;
                background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMVY4TTE1IDhIOE04IDE1VjhNMSA4SDgiIHN0cm9rZT0iIzE4OTBGRiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+') no-repeat center;
                background-size: contain;
                margin-right: 8px;
              }
            }
          }
        }

        >div {
          padding: 12px 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 40px;
          // border-right: 1px solid #f0f0f0;

          &:last-child {
            border-right: none;
          }
        }

        .col-serial {
          width: 80px;
        }

        .col-description {
          width: 200px;
        }

        .col-files {
          flex: 1;
          min-width: 180px;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          gap: 4px;

          .files-content {
            width: 100%;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 4px;
            flex-wrap: wrap;
            justify-content: flex-start;

            .file-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 4px;
              justify-content: flex-start;
              max-width: 100%;

              .file-tag {
                cursor: pointer;
                font-size: 12px;
                background-color: #e6f7ff;
                border-color: #1890ff;
                color: #1890ff;
                display: flex;
                align-items: center;
                gap: 2px;

                &:hover {
                  opacity: 0.8;
                  background-color: #bae7ff;
                }
              }
            }
          }
        }

        .col-operation {
          width: 100px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 12px 8px;

          .ant-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 4px;
            height: 32px;
            width: 32px;
            min-width: 32px;
            border-radius: 4px;

            :deep(.anticon) {
              font-size: 16px;
              line-height: 1;
              margin: 0;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .ant-popconfirm {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }

  // 无边框输入框样式
  .borderless-input {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;

    &:focus,
    &:hover {
      border: none !important;
      box-shadow: none !important;
    }

    .ant-input {
      border: none !important;
      box-shadow: none !important;
      background: transparent !important;
    }

    .ant-picker-input>input {
      border: none !important;
      box-shadow: none !important;
    }
  }

  .preview-content {
    .preview-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      h4 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }

    .preview-body {
      text-align: center;

      .file-download {
        padding: 40px 0;

        p {
          margin-bottom: 16px;
          color: #666;
        }
      }

      .no-file {
        padding: 40px 0;
        color: #999;

        p {
          margin: 8px 0;
        }
      }
    }
  }
}

.mr8 {
  margin-right: 8px;
}

.mr20 {
  margin-right: 20px;
}

// 全局样式覆盖
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-select) {
  width: 100%;
}

:deep(.ant-picker) {
  width: 100%;
}

// 查看模式纯文本样式
.readonly-text {
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  padding: 4px 0;
  display: inline-block;
  width: 100%;
  text-align: center;

  &.small {
    font-size: 12px;
    color: #999;
  }
}

.file-tag-wrapper {
  position: relative;
  display: inline-flex;
  align-items: center;

  .file-tag {
    cursor: pointer;
    font-size: 12px;
    background-color: #e6f7ff;
    border-color: #1890ff;
    color: #1890ff;
    padding-right: 20px; // 为删除按钮预留空间
    margin-right: 0;

    &:hover {
      opacity: 0.8;
      background-color: #bae7ff;
    }
  }

  .file-delete-btn {
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
    width: 14px;
    height: 14px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    line-height: 1;
    border: none;
    background: transparent;

    &:hover {
      background-color: rgba(255, 77, 79, 0.1);
      border-radius: 2px;
    }
  }
}
</style>
