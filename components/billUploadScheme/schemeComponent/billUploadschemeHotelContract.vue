<script setup lang="ts">
// 一手合同组件
import { message } from 'ant-design-vue';
import { onMounted, ref, watch, nextTick, defineProps, defineEmits, computed } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { fileApi } from '@haierbusiness-front/apis';
import { UploadFile, HotelItem, SUPPLEMENT_FILE_CONFIG } from '@haierbusiness-front/common-libs';

// 注意：HotelItem 类型定义已移至 @haierbusiness-front/common-libs/micemerchant

// 文件上传相关常量（从 common-libs 导入）
const { SUPPORTED_FILE_TYPES, FILE_SIZE_LIMIT, UPLOAD_ACCEPT } = SUPPLEMENT_FILE_CONFIG;

const props = defineProps({
  hotelList: {
    type: Array as () => HotelItem[],
    default: () => [],
  },
  contractData: {
    type: Array,
    default: () => [],
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['hotelContractEmit']);

// 响应式数据
const loading = ref(false);
const uploadLoading = ref<Record<string, boolean>>({});
const previewVisible = ref(false);
const previewFile = ref<UploadFile | null>(null);
const previewFileName = ref('');

// 获取基础URL
const baseUrl = import.meta.env.VITE_BUSINESS_URL || '';

// 锚点跳转函数
const anchorJump = (id: string) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};

// 初始化标志，防止重复初始化
const isInitialized = ref(false);

// 🔥 查看模式专用：虚拟酒店列表（从 contractData 创建）
const virtualHotelList = ref<HotelItem[]>([]);

// 计算属性：根据模式返回合适的酒店列表
const displayHotelList = computed(() => {
  let result;
  if (props.readonly && virtualHotelList.value.length > 0) {
    result = virtualHotelList.value;
  } else {
    result = props.hotelList || [];
  }

  return result;
});



// 查看模式专用：从 contractData 创建虚拟酒店列表并初始化
const initContractFromCacheForViewMode = (cacheData: any[]) => {

  const tempHotelList: HotelItem[] = [];

  // 遍历缓存的合同数据，为每个酒店创建虚拟条目
  cacheData.forEach((contractItem: any) => {
    const { subType, paths } = contractItem;

    if (subType && paths && Array.isArray(paths)) {
      // 将路径转换为 UploadFile 对象
      const contractFiles: UploadFile[] = paths.map((path: string, index: number) => {
        // 处理路径，确保正确的 URL 格式
        let processedPath = path;

        // 如果路径已经包含完整 URL，提取相对路径
        if (path.includes(baseUrl)) {
          processedPath = path.replace(baseUrl, '');
        }

        // 确保路径以 / 开头
        if (!processedPath.startsWith('/')) {
          processedPath = '/' + processedPath;
        }

        // 生成文件名
        const fileName = path.split('/').pop() || `合同文件${index + 1}`;

        return {
          uid: `${subType}-${index}`,
          name: fileName,
          status: 'done',
          url: baseUrl + processedPath,
          filePath: baseUrl + processedPath,
        } as UploadFile;
      });

      // 创建虚拟酒店条目
      const virtualHotel: HotelItem = {
        id: `virtual-${subType}`,
        hotelName: subType,
        contractFiles: contractFiles,
      };

      tempHotelList.push(virtualHotel);
    }
  });

  virtualHotelList.value = tempHotelList;
};

// 从缓存数据初始化一手合同
const initContractFromCache = (cacheData: any[]) => {
  if (!cacheData || cacheData.length === 0) {
    return;
  }

  // 查看模式下，如果 hotelList 为空，从 contractData 创建虚拟酒店列表
  if (props.readonly && (!props.hotelList || props.hotelList.length === 0)) {
    initContractFromCacheForViewMode(cacheData);
    return;
  }

  // 编辑模式或有 hotelList 的情况，使用原有逻辑
  if (!props.hotelList || props.hotelList.length === 0) {
    return;
  }


  // 遍历缓存的合同数据
  cacheData.forEach((contractItem: any) => {
    const { subType, paths } = contractItem;

    // 根据 subType（酒店名称）找到对应的酒店
    const hotel = props.hotelList.find((h: HotelItem) => h.hotelName === subType);

    if (hotel && paths && Array.isArray(paths)) {
      // 将路径转换为 UploadFile 对象
      const contractFiles: UploadFile[] = paths.map((path: string, index: number) => {
        // 处理路径，确保正确的 URL 格式
        let processedPath = path;

        // 如果路径已经包含完整 URL，提取相对路径
        if (path.includes(baseUrl)) {
          processedPath = path.replace(baseUrl, '');
        }

        // 确保路径以 / 开头
        if (!processedPath.startsWith('/')) {
          processedPath = '/' + processedPath;
        }

        return {
          uid: `${hotel.id}_cache_${index}_${Date.now()}`,
          name: path.split('/').pop() || `合同${index + 1}`,
          status: 'done' as const,
          url: baseUrl + processedPath,
          filePath: baseUrl + processedPath,
          fileName: path.split('/').pop() || `合同${index + 1}`,
        };
      });

      // 将文件添加到酒店的合同文件列表（缓存恢复时直接替换，避免重复）
      hotel.contractFiles = contractFiles;
    } else {
    }
  });

  isInitialized.value = true;

  // 发射数据到父组件
  emit('hotelContractEmit', getHotelFilesData());
};

// 获取所有酒店的文件数据
const getHotelFilesData = () => {
  const hotelFilesData: any[] = [];
  props.hotelList.forEach((hotel) => {
    if (hotel.contractFiles && hotel.contractFiles.length > 0) {
      const paths = hotel.contractFiles
        .map((file) => {
          if (file.filePath) {
            // 返回完整路径，保留baseUrl前缀
            return file.filePath;
          }
          return '';
        })
        .filter((path) => path); // 过滤掉空路径

      if (paths.length > 0) {
        hotelFilesData.push({
          subType: hotel.hotelName, // 酒店名称作为subType
          paths: paths, // 文件路径列表
        });
      }
    }
  });
  return hotelFilesData;
};

// 文件上传前验证
const beforeUpload = (file: File): boolean => {
  const isValidType =
    SUPPORTED_FILE_TYPES.includes(file.type) ||
    file.name.toLowerCase().endsWith('.pdf') ||
    file.name.toLowerCase().endsWith('.doc') ||
    file.name.toLowerCase().endsWith('.docx');

  if (!isValidType) {
    message.error('只支持上传 PDF、图片、Word 文档格式的文件！');
    return false;
  }

  const isValidSize = file.size / 1024 / 1024 < FILE_SIZE_LIMIT;
  if (!isValidSize) {
    message.error(`文件大小不能超过 ${FILE_SIZE_LIMIT}MB！`);
    return false;
  }

  return true; // 验证通过，允许继续自定义上传
};

// 获取文件显示名称（只显示横杠后面的部分）
const getFileDisplayName = (fileName: string): string => {
  if (!fileName) return '';

  // 先提取文件名（去掉路径）
  const baseFileName = fileName.split('/').pop() || fileName;

  // 如果包含横杠，只显示最后一个横杠后面的部分
  const dashIndex = baseFileName.lastIndexOf('-');
  let displayName = dashIndex !== -1 ? baseFileName.substring(dashIndex + 1) : baseFileName;

  // 如果显示名称太长，进行截断
  const maxLength = 20;
  if (displayName.length <= maxLength) return displayName;

  const extension = displayName.split('.').pop() || '';
  const nameWithoutExt = displayName.substring(0, displayName.lastIndexOf('.'));
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';

  return `${truncatedName}.${extension}`;
};

// 文件上传处理
const uploadRequest = (options: any, hotelId: string) => {
  if (props.readonly) {
    message.warning('查看模式下不能上传文件');
    return;
  }

  uploadLoading.value[hotelId] = true;

  const formData = new FormData();
  formData.append('file', options.file);

  fileApi
    .upload(formData)
    .then((it) => {
      // 构建文件对象用于显示
      const fileObj: UploadFile = {
        uid: options.file.uid || Date.now().toString(),
        name: options.file.name,
        status: 'done',
        url: it.path ? baseUrl + it.path : '',
        filePath: it.path ? baseUrl + it.path : '',
        fileName: options.file.name,
      };

      // 添加到对应酒店的文件列表（本地显示用）
      const hotel = props.hotelList.find((h) => h.id === hotelId);
      if (hotel) {
        hotel.contractFiles.push(fileObj);


        // 向父组件传递酒店文件数据
        emit('hotelContractEmit', getHotelFilesData());
      }

      message.success(`${options.file.name} 上传成功`);
    })
    .catch(() => {
      message.error(`${options.file.name} 上传失败，请重试`);
    })
    .finally(() => {
      uploadLoading.value[hotelId] = false;
    });
};

// 文件预览
const handlePreview = (file: UploadFile) => {
  previewFile.value = file;
  previewFileName.value = file.name;
  previewVisible.value = true;
};

// 校验
const hotelContractSub = () => {
  let isVerPassed = true;
  isVerPassed = !props.hotelList.some(item => {
    if (!item.contractFiles?.length) {
      message.error('请添加合同附件！');
      anchorJump('hotelContractSection');
      return true; // 终止遍历:ml-citation{ref="4" data="citationList"}
    }
  });

  if (isVerPassed) {
    emit('hotelContractEmit', getHotelFilesData());
  }

  return isVerPassed;
};

defineExpose({ hotelContractSub });

// 文件删除
const handleRemove = (file: UploadFile, hotelId: string) => {
  if (props.readonly) {
    message.warning('查看模式下不能删除文件');
    return;
  }

  const hotel = props.hotelList.find((h) => h.id === hotelId);
  if (hotel) {
    const index = hotel.contractFiles.findIndex((f) => f.uid === file.uid);
    if (index > -1) {
      // 从列表中移除文件（本地显示用）
      hotel.contractFiles.splice(index, 1);


      // 向父组件传递酒店文件数据
      emit('hotelContractEmit', getHotelFilesData());
      message.success('文件删除成功');
    }
  }
};

// 关闭预览
const handlePreviewCancel = () => {
  previewVisible.value = false;
  previewFile.value = null;
  previewFileName.value = '';
};

// 下载文件
const handleDownloadFile = () => {
  if (previewFile.value && previewFile.value.url) {
    window.open(previewFile.value.url, '_blank');
  }
};

// 防重复处理的标记
const lastProcessedContractData = ref<string>('');
const lastProcessedHotelList = ref<string>('');

// 监听 contractData 变化，处理缓存数据回显
watch(
  () => props.contractData,
  (newContractData) => {
    // 防重复：检查数据是否真的变化了
    const currentDataKey = JSON.stringify(newContractData);

    if (lastProcessedContractData.value === currentDataKey) {
      return;
    }

    if (newContractData && newContractData.length > 0) {
      if (props.readonly) {
        nextTick(() => {
          if (props.hotelList && props.hotelList.length > 0) {
            initContractFromCache(newContractData);
            lastProcessedContractData.value = currentDataKey;
          } else {
            initContractFromCacheForViewMode(newContractData);
            lastProcessedContractData.value = currentDataKey;
          }
        });
      } else {
        nextTick(() => {
          if (props.hotelList && props.hotelList.length > 0) {
            initContractFromCache(newContractData);
            lastProcessedContractData.value = currentDataKey;
          }
        });
      }
    }
  },
  { immediate: true, deep: true },
);

// 监听 hotelList 变化，如果有缓存数据但还未初始化，则进行初始化
watch(
  () => props.hotelList,
  (newHotelList) => {
    // 防重复：检查数据是否真的变化了
    const currentHotelKey = JSON.stringify(newHotelList?.map(h => ({ id: h.id, name: h.hotelName })));
    if (lastProcessedHotelList.value === currentHotelKey) {
      return;
    }

    if (props.contractData && props.contractData.length > 0) {
      if (props.readonly) {
        nextTick(() => {
          initContractFromCache(props.contractData);
          lastProcessedHotelList.value = currentHotelKey;
        });
      } else if (newHotelList && newHotelList.length > 0) {
        nextTick(() => {
          initContractFromCache(props.contractData);
          lastProcessedHotelList.value = currentHotelKey;
        });
      }
    }
  },
  { immediate: true, deep: true },
);

onMounted(() => {

  // 初始化时检查是否有缓存数据需要恢复
  if (props.contractData && props.contractData.length > 0) {
    if (props.readonly) {
      // 查看模式：根据 hotelList 是否为空选择不同策略
      if (props.hotelList && props.hotelList.length > 0) {
        // 有 hotelList 时使用原有逻辑
        nextTick(() => {
          initContractFromCache(props.contractData);
        });
      } else {
        // 查看模式且 hotelList 为空时，使用虚拟酒店列表逻辑
        nextTick(() => {
          initContractFromCacheForViewMode(props.contractData);
        });
      }
    } else if (props.hotelList && props.hotelList.length > 0 && !isInitialized.value) {
      // 编辑模式：保持原有逻辑不变
      nextTick(() => {
        initContractFromCache(props.contractData);
      });
    }
  }
});
</script>

<template>
  <!-- 一手合同 -->
  <div class="hotel-contract-section" id="hotelContractSection">
    <div class="contract_title">
      <div class="interact_shu mr20"></div>
      <span>一手合同</span>
    </div>
    <div class="contract_wrapper">
      <a-spin :spinning="loading">
        <div v-for="hotel in displayHotelList" :key="hotel.id" class="hotel-contract-item">
          <a-tooltip :title="hotel.hotelName">
            <div class="hotel-name">{{ hotel.hotelName }}</div>
          </a-tooltip>
          <div class="contract-files">
            <!-- 已上传文件标签区域 -->
            <div class="file-tags">
              <template v-if="hotel.contractFiles && hotel.contractFiles.length > 0">
                <span v-for="file in hotel.contractFiles" :key="file.uid" class="file-tag-wrapper">
                  <a-tag class="file-tag"
                  @click="() => handlePreview(file)">
                  {{ getFileDisplayName(file.name) }}
                </a-tag>
                  <a-popconfirm v-if="!readonly" :title="`确认删除文件 ${file.name}？`" description="删除后无法恢复" ok-text="删除"
                    cancel-text="取消" @confirm="() => handleRemove(file, hotel.id)">
                    <a-button type="text" size="small" danger class="file-delete-btn">×</a-button>
                  </a-popconfirm>
                </span>
                
              </template>
              <template v-else-if="!readonly">
                <span class="file-placeholder"></span>
              </template>
              <template v-else>
                <span class="readonly-text">暂无附件</span>
              </template>
            </div>
            <!-- 上传按钮 - 查看模式下隐藏 -->
            <a-upload v-if="!readonly" :file-list="[]" :before-upload="beforeUpload"
              :custom-request="(options: any) => uploadRequest(options, hotel.id)" :multiple="true"
              :show-upload-list="false" :accept="UPLOAD_ACCEPT">
              <a-button class="btn-style" size="small" type="primary" ghost :loading="uploadLoading[hotel.id] || false">
                <upload-outlined />
                上传附件
              </a-button>
            </a-upload>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 文件预览弹框 -->
    <a-modal v-model:open="previewVisible" title="文件预览" :footer="null" width="80%" @cancel="handlePreviewCancel">
      <div class="preview-content">
        <div class="preview-header">
          <h4>{{ previewFileName }}</h4>
        </div>
        <div class="preview-body">
          <template v-if="previewFile && previewFile.url">
            <!-- 图片预览 -->
            <img v-if="
              previewFile.name &&
              (previewFile.name.toLowerCase().includes('.jpg') ||
                previewFile.name.toLowerCase().includes('.jpeg') ||
                previewFile.name.toLowerCase().includes('.png') ||
                previewFile.name.toLowerCase().includes('.gif'))
            " :src="previewFile.url" alt="预览图片" style="max-width: 100%; max-height: 500px; object-fit: contain" />
            <!-- PDF预览 -->
            <iframe v-else-if="previewFile.name && previewFile.name.toLowerCase().includes('.pdf')"
              :src="previewFile.url" style="width: 100%; height: 500px; border: none"></iframe>
            <!-- 其他文件类型显示下载链接 -->
            <div v-else class="file-download">
              <p>无法预览此文件类型，请下载查看</p>
              <a-button type="primary" @click="handleDownloadFile"> 下载文件 </a-button>
            </div>
          </template>
          <template v-else>
            <div class="no-file">
              <p>文件信息：{{ previewFileName }}</p>
              <p>暂无可预览的文件内容</p>
            </div>
          </template>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<style scoped lang="less">
.hotel-contract-section {
  padding-top: 24px;

  .interact_shu {
    width: 4px;
    height: 20px;
    background: #1868db;
    border-radius: 2px;
  }

  .contract_title {
    margin-bottom: 20px;
    font-size: 18px;
    color: #333;
    // font-weight: 500;
    display: flex;
    align-items: center;
    // gap: 8px;
    font-family: PingFangSC, PingFang SC;

    span {
      font-size: 18px;
      // font-weight: 500;
      color: #1d2129;
    }
  }

  .contract_wrapper {
    border-radius: 6px;
    overflow: hidden;
    width: 70%;

    .hotel-contract-item {
      display: flex;
      align-items: center;
      padding: 18px 30px;
      padding-top: 0;
      background-color: #fff;
      transition: background-color 0.3s;

      &:hover {
        background-color: #fafafa;
      }

      .hotel-name {
        width: 200px;
        font-size: 14px;
        color: #333;
        margin-right: 40px;
        flex-shrink: 0;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .contract-files {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 24px;

        .file-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-right: 20px;
          min-width: 220px;
          min-height: 32px;
          align-items: center;

          .file-tag {
            cursor: pointer;
            font-size: 12px;
            background-color: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
            padding: 4px 20px;
            margin: 2px 0;

            &:hover {
              opacity: 0.8;
              background-color: #bae7ff;
            }
          }

          .file-placeholder {
            display: inline-block;
            width: 0;
            height: 0;
          }
        }

        :deep(.btn-style) {
          height: 32px;
          font-size: 12px;
          padding: 4px 12px;
        }
      }
    }
  }

  .preview-content {
    .preview-header {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      h4 {
        margin: 0;
        font-size: 16px;
        color: #333;
      }
    }

    .preview-body {
      text-align: center;

      .file-download {
        padding: 40px 0;

        p {
          margin-bottom: 16px;
          color: #666;
        }
      }

      .no-file {
        padding: 40px 0;
        color: #999;

        p {
          margin: 8px 0;
        }
      }
    }
  }

  // 查看模式纯文本样式
  .readonly-text {
    color: #999;
    font-size: 14px;
    font-style: italic;
  }
}
.file-tag-wrapper {
  position: relative;
  display: inline-flex;
  align-items: center;

  .file-tag {
    cursor: pointer;
    font-size: 12px;
    background-color: #e6f7ff;
    border-color: #1890ff;
    color: #1890ff;
    padding-right: 20px; // 为删除按钮预留空间
    margin-right: 0;

    &:hover {
      opacity: 0.8;
      background-color: #bae7ff;
    }
  }

  .file-delete-btn {
    position: absolute;
    right: 2px;
    top: 50%;
    transform: translateY(-50%);
    width: 14px;
    height: 14px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    line-height: 1;
    border: none;
    background: transparent;

    &:hover {
      background-color: rgba(255, 77, 79, 0.1);
      border-radius: 2px;
    }
  }
}
</style>
