import { IPageRequest } from '../../basic';
import dayjs, { Dayjs } from 'dayjs';
/* 议程管理列表 */
export class IAgendaFilter extends IPageRequest {
  /* 会议ID */
  miceId?: number | null;

}
/* 议程管理 */
export class IMeetingAgenda {
  /*主键 */
  id?: number;

  /*会中信息表id */
  miceInfoId?: number;

  /*日期 */
  agendaDate?: Dayjs | string;

  /*地点 */
  address?: string;

  /*开始时间 */
  startTime?: Dayjs | string;

  /*结束时间 */
  endTime?: Dayjs | string;

  /*会议内容 */
  subject?: string;

  /*发言人 */
  speaker?: string;

  /*排序 */
  sort?: number;
}
